"use client";

import { memo, useCallback, useEffect, useState, useMemo } from "react";
import { SearchIcon, StarIcon, SwapIcon } from "@/assets/icons";
import { AppButtonSort } from "@/components/AppButtonSort";
import rf from "@/services/RequestFactory";
import { TPairMarket, TPairSetting, TradingPair } from "@/types/pair";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/store";
import { useRouter } from "next/navigation";
import { MarketPairRow } from "./components/MarketPairRow";
import {
  toggleFavoritePair,
  loadFavoritesFromStorage,
} from "@/store/favorites.store";
import { useMultipleTickers } from "@/hooks/useTicker";

const TABS = [
  { name: "All", value: "all" },
  { name: "Favorites", value: "favorites" },
];

export const MarketExplorerOptimized = memo(() => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { favoritePairs } = useSelector((state: RootState) => state.favorites);

  // Use the optimized ticker hook
  const { tickers, isConnected, renderCount } = useMultipleTickers();

  const [search, setSearch] = useState("");
  const [tabActive, setTabActive] = useState("all");
  const [isShow24hChange, setIsShow24hChange] = useState(true);
  const [sortBy, setSortBy] = useState("");
  const [sortType, setSortType] = useState("");
  const [tradingPairs, setTradingPairs] = useState<TPairSetting[]>([]);
  const [pairMarkets, setPairMarkets] = useState<TPairMarket[]>([]);

  useEffect(() => {
    dispatch(loadFavoritesFromStorage());
  }, [dispatch]);

  const toggleFavorite = useCallback(
    (symbol: string) => {
      dispatch(toggleFavoritePair(symbol));
    },
    [dispatch]
  );

  // Fetch trading pairs and markets
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [pairsResponse, marketsResponse] = await Promise.all([
          rf.getRequest().PairRequest.getTradingPairs(),
          rf.getRequest().PairRequest.getPairMarket(),
        ]);

        if (pairsResponse?.success) {
          setTradingPairs(pairsResponse.data || []);
        }

        if (marketsResponse?.success) {
          setPairMarkets(marketsResponse.data || []);
        }
      } catch (error) {
        console.error("Error fetching trading data:", error);
      }
    };

    fetchData();
  }, []);

  // Optimized filtering and sorting with memoization
  const tradingPairsFiltered = useMemo(() => {
    let filtered = tradingPairs;

    // Filter by search
    if (search) {
      filtered = filtered.filter((item) =>
        item.symbol.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Filter by tab
    if (tabActive === "favorites") {
      filtered = filtered.filter((item) =>
        favoritePairs.includes(item.symbol)
      );
    } else if (tabActive !== "all") {
      filtered = filtered.filter((item) => item.quoteAsset === tabActive);
    }

    // Sort
    if (sortBy && sortType) {
      filtered = [...filtered].sort((a, b) => {
        const tickerA = tickers[a.symbol];
        const tickerB = tickers[b.symbol];

        let valueA: number = 0;
        let valueB: number = 0;

        switch (sortBy) {
          case "pair":
            return sortType === "asc"
              ? a.symbol.localeCompare(b.symbol)
              : b.symbol.localeCompare(a.symbol);
          case "price":
            valueA = parseFloat(tickerA?.lastPrice || "0");
            valueB = parseFloat(tickerB?.lastPrice || "0");
            break;
          case "24Change":
            valueA = parseFloat(tickerA?.priceChangePercent || "0");
            valueB = parseFloat(tickerB?.priceChangePercent || "0");
            break;
          case "Volume":
            valueA = parseFloat(tickerA?.baseVolume || "0");
            valueB = parseFloat(tickerB?.baseVolume || "0");
            break;
          default:
            return 0;
        }

        return sortType === "asc" ? valueA - valueB : valueB - valueA;
      });
    }

    return filtered;
  }, [
    tradingPairs,
    search,
    tabActive,
    favoritePairs,
    sortBy,
    sortType,
    tickers,
    renderCount, // Include renderCount to re-run when ticker data changes
  ]);

  const handlePairClick = (symbol: string) => {
    router.replace(`/trade/${symbol}`);
  };

  return (
    <div className="border-white-100 h-[420px] border-b p-3">
      <div className="border-white-100 flex items-center gap-2 rounded-[6px] border px-2 py-2">
        <SearchIcon className="text-white-500" />
        <input
          className="body-md-regular-14 placeholder:text-white-300 bg-transparent outline-none"
          placeholder="Search"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      <div className="border-white-50 flex border-b">
        {[...TABS, ...pairMarkets].map((item: TPairMarket, index) => {
          return (
            <div
              onClick={() => setTabActive(item.value)}
              className={`flex h-[40px] cursor-pointer items-center px-3 py-2.5 ${
                item.value === tabActive
                  ? "text-white-1000 body-sm-regular-12 border-white-500 border-b"
                  : "text-white-500 body-sm-regular-12"
              }`}
              key={index}
            >
              {item.name}
            </div>
          );
        })}
      </div>

      <div>
        <div className="flex justify-between">
          <div className="body-sm-regular-12 text-white-500 flex items-center gap-2 p-2">
            Pair{" "}
            <AppButtonSort
              value="pair"
              sortBy={sortBy}
              sortType={sortType}
              setSortType={setSortType}
              setSortBy={setSortBy}
            />
          </div>
          <div className="body-sm-regular-12 text-white-500 flex items-center py-2 text-right">
            <div className="flex items-center gap-2">
              Last price
              <AppButtonSort
                value="price"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
            <div className="ml-1 flex items-center gap-2">
              / {isShow24hChange ? "24h Change" : "Vol"}{" "}
              <AppButtonSort
                value={isShow24hChange ? "24Change" : "Volume"}
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>{" "}
            <SwapIcon
              onClick={() => setIsShow24hChange(!isShow24hChange)}
              className="hover:text-white-1000 ml-0.5 cursor-pointer"
            />
          </div>
        </div>

        <div className="-mx-3">
          {/* Use memoized rows for better performance */}
          {tradingPairsFiltered?.map((item, index) => (
            <MarketPairRow
              key={item.symbol || index}
              item={item}
              ticker={tickers[item.symbol]}
              isShow24hChange={isShow24hChange}
              isFavorite={favoritePairs.includes(item.symbol)}
              onToggleFavorite={toggleFavorite}
              onPairClick={handlePairClick}
            />
          ))}
        </div>
      </div>
    </div>
  );
});

MarketExplorerOptimized.displayName = "MarketExplorerOptimized";

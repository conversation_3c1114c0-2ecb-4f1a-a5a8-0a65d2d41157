import { usePairContext } from "@/app/trade/[symbol]/provider";
import { ChevronDownIcon } from "@/assets/icons";
import { AppPopover } from "@/components/AppPopover";
import React, { useEffect, useState } from "react";

const OPTIONS_DECIMAL = [0.001, 0.01, 0.1, 1, 10, 100];

const SelectDecimal = ({
  setDecimals,
  decimals,
}: {
  setDecimals: (value: number) => void;
  decimals: number;
}) => {
  const [isShowDecimal, setShowDecimal] = useState<boolean>(false);
  const { pairSetting } = usePairContext();
  const [decimalOptions, setDecimalOptions] = useState(OPTIONS_DECIMAL);

  useEffect(() => {
    if (!pairSetting?.pricePrecision) {
      return;
    }

    const initDecimalOptions = () => {
      const result = [];

      // decimal to array, example: from 2 to [0.001, 0.01, 0.1]
      for (let i = pairSetting.pricePrecision; i >= 0; i--) {
        const decimalValue = 1 / Math.pow(10, i);
        result.push(decimalValue);
      }

      setDecimalOptions(result);
      setDecimals(result[0] || 0.001);
    };

    initDecimalOptions();
  }, [pairSetting?.pricePrecision]);

  return (
    <AppPopover
      trigger={
        <div className="body-md-medium-14 flex cursor-pointer items-center gap-1">
          {decimals} <ChevronDownIcon />
        </div>
      }
      onClose={() => setShowDecimal(false)}
      content={
        <div
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
          }}
          className="flex min-w-[70px] flex-col gap-2 rounded-[8px] p-3"
        >
          {decimalOptions.map((item) => {
            return (
              <div
                key={item}
                className={`body-sm-medium-12 hover:text-white-1000 cursor-pointer ${
                  item === decimals ? "text-white-1000" : "text-white-500"
                }`}
                onClick={() => {
                  setDecimals(item);
                  setShowDecimal(false);
                }}
              >
                {item}
              </div>
            );
          })}
        </div>
      }
      isOpen={isShowDecimal}
      onToggle={() => setShowDecimal(!isShowDecimal)}
    />
  );
};

export default SelectDecimal;

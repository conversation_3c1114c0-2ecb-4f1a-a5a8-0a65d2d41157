import React, { useCallback, useState } from "react";
import { EOrderbook, OrderBookLevel } from "@/types/OrderBook";
import BigNumber from "bignumber.js";
import { getDecimalPlaces } from "@/utils/helper";
import { useProcessedOrderbook } from "../hooks/useProcessedOrderbook";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { OrderbookTooltip } from "./OrderBookTooltip";
import AppNumber from "@/components/AppNumber";
import { usePairContext } from "@/app/trade/[symbol]/provider";

const OrderRow = React.memo(
  ({
    item,
    percentWidth,
    decimal,
    orderbookType,
    quantityDecimal,
    isHideTotal = false,
    onHover,
    onLeave,
    index,
  }: {
    item: {
      price: string;
      amount: string;
      total: string;
      cumulative: string;
    };
    quantityDecimal: number;
    percentWidth: string;
    decimal: number;
    orderbookType: EOrderbook;
    isHideTotal?: boolean;
    onHover: (index: number, event: React.MouseEvent) => void;
    onLeave: () => void;
    index: number;
  }) => {
    let textColor = "text-red-400";
    let bgColor = "red";

    if (orderbookType === EOrderbook.BID) {
      textColor = "text-green-500";
      bgColor = "green";
    }

    const handleClickItem = () => {
      if (!item.price) return;

      AppBroadcast.dispatch(BROADCAST_EVENTS.ORDERBOOK_SELECTED, {
        price: item.price,
        amount: item.amount,
        orderbookType,
      });
    };

    const handleMouseEnter = (event: React.MouseEvent) => {
      onHover(index, event);
    };

    return (
      <div
        className="relative cursor-pointer"
        onClick={handleClickItem}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={onLeave}
      >
        <div className={`grid ${isHideTotal ? "grid-cols-2" : "grid-cols-3"}`}>
          <div
            className={`body-sm-regular-12 px-2 py-1 ${textColor} ${
              orderbookType == EOrderbook.BID && isHideTotal ? "order-2" : ""
            }`}
          >
            <AppNumber
              value={item.price || 0}
              decimals={getDecimalPlaces(decimal)}
              isFormatLargeNumber={false}
            />
          </div>
          <div
            className={`${
              orderbookType == EOrderbook.BID && isHideTotal
                ? "order-1"
                : "text-right"
            } body-sm-regular-12 px-2 py-1 `}
          >
            <AppNumber
              value={item.amount || 0}
              decimals={quantityDecimal}
              isFormatLargeNumber={false}
            />
          </div>

          {!isHideTotal && (
            <div className="body-sm-regular-12 px-2 py-1 text-right">
              <AppNumber value={item?.total || 0} decimals={quantityDecimal} />
            </div>
          )}
        </div>
        <div
          className={`absolute bottom-0 right-0 top-0 h-full bg-${bgColor}-900`}
          style={{ width: `${percentWidth}%` }}
        />
      </div>
    );
  }
);
OrderRow.displayName = "OrderRow";

export const ListOrder = React.memo(
  ({
    orderbook,
    decimal,
    orderbookType,
    displayItemNumber,
    isHideTotal = false,
  }: {
    orderbook: OrderBookLevel[];
    decimal: number;
    orderbookType: EOrderbook;
    displayItemNumber: number;
    isHideTotal?: boolean;
  }) => {
    const { bidAskData, largestAmount } = useProcessedOrderbook(
      orderbook,
      decimal,
      displayItemNumber,
      orderbookType === EOrderbook.ASK
    );
    const { pairSetting } = usePairContext();

    const [tooltip, setTooltip] = useState<{
      isVisible: boolean;
      position: { x: number; y: number };
      data: {
        totalQuantity: string;
        totalAmount: string;
        avgPrice: string;
      };
    }>({
      isVisible: false,
      position: { x: 0, y: 0 },
      data: {
        totalQuantity: "0",
        totalAmount: "0",
        avgPrice: "0",
      },
    });

    const calculatePercentWidth = useCallback(
      (total: string) => {
        return BigNumber(total)
          .div(largestAmount || 1)
          .multipliedBy(100)
          .toFixed(0);
      },
      [largestAmount]
    );

    const calculateTooltipData = useCallback(
      (hoveredIndex: number) => {
        if (!bidAskData)
          return { totalQuantity: "0", totalAmount: "0", avgPrice: "0" };

        let totalQuantity = new BigNumber(0);
        let totalAmount = new BigNumber(0);

        for (let i = 0; i <= hoveredIndex; i++) {
          const item = bidAskData[i];
          if (item) {
            totalQuantity = totalQuantity.plus(item.amount);
            totalAmount = totalAmount.plus(item.total);
          }
        }

        const avgPrice = totalQuantity.isZero()
          ? new BigNumber(0)
          : totalAmount.div(totalQuantity);

        return {
          totalQuantity: totalQuantity.toFixed(),
          totalAmount: totalAmount.toFixed(),
          avgPrice: avgPrice.toFixed(),
        };
      },
      [bidAskData]
    );

    const handleRowHover = useCallback(
      (index: number, event: React.MouseEvent) => {
        const rect = event.currentTarget.getBoundingClientRect();
        const tooltipData = calculateTooltipData(index);

        setTooltip({
          isVisible: true,
          position: {
            x: rect.left + rect.width / 2,
            y: rect.top,
          },
          data: tooltipData,
        });
      },
      [calculateTooltipData]
    );

    const handleRowLeave = useCallback(() => {
      setTooltip((prev) => ({ ...prev, isVisible: false }));
    }, []);

    return (
      <div className="will-change-transform">
        {bidAskData?.map((item, index) => {
          const percentWidth = calculatePercentWidth(item.total);
          return (
            <OrderRow
              key={`${item.price}-${index}`}
              item={item}
              isHideTotal={isHideTotal}
              percentWidth={percentWidth}
              orderbookType={orderbookType}
              quantityDecimal={pairSetting?.quantityPrecision || 1}
              decimal={decimal}
              onHover={handleRowHover}
              onLeave={handleRowLeave}
              index={index}
            />
          );
        })}

        <OrderbookTooltip
          isVisible={tooltip.isVisible}
          position={tooltip.position}
          data={tooltip.data}
          decimal={decimal}
        />
      </div>
    );
  }
);
ListOrder.displayName = "ListOrder";

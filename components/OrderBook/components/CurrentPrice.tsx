import { usePairContext } from "@/app/trade/[symbol]/provider";
import { ArrowDown } from "@/assets/icons";
import { RootState } from "@/store";
import { formatPrice } from "@/utils/format";
import { getPriceStyle } from "@/utils/helper";
import React from "react";
import { useSelector } from "react-redux";

const CurrentPrice = () => {
  const { pairSetting } = usePairContext();
  const { tradingPair } = useSelector((state: RootState) => state.tradingPair);

  return (
    <div className="flex items-center gap-1 p-2">
      <div
        className="heading-lg-medium-24"
        style={{
          color: getPriceStyle(tradingPair.latestChange || "0"),
        }}
      >
        {formatPrice(tradingPair?.lastPrice || 0, pairSetting?.pricePrecision)}
      </div>
      <ArrowDown
        style={{
          color: getPriceStyle(tradingPair.latestChange || "0"),
          transform: tradingPair.isUp && "rotate(180deg)",
        }}
      />

      <div className="body-sm-regular-12 text-white-500">
        ${formatPrice(tradingPair?.lastPrice || 0, pairSetting?.pricePrecision)}
      </div>
    </div>
  );
};

export default CurrentPrice;

import {
  StarIcon,
  ChevronDownIcon,
  SearchIcon,
  StarActiveIcon,
} from "@/assets/icons";
import React, { useEffect, useRef, useState } from "react";
import { AppButtonSort } from "@/components/AppButtonSort";
import { usePairContext } from "@/app/trade/[symbol]/provider";

const TAB_KEYS = {
  FAVOURITE: "FAVOURITE",
  HOLDING: "HOLDING",
  ALL: "ALL",
  NEW: "NEW",
};

const TABS = [
  {
    name: <StarIcon />,
    value: TAB_KEYS.FAVOURITE,
  },
  {
    name: "Holding",
    value: TAB_KEYS.HOLDING,
  },
  {
    name: "All",
    value: TAB_KEYS.ALL,
  },
  {
    name: "New",
    value: TAB_KEYS.NEW,
  },
];

export const PairSymbol = ({ symbol }: { symbol: string }) => {
  const [isShow, setIsShow] = useState(false);
  const contentRef = useRef<any>(null);
  const [search, setSearch] = useState<string>("");
  const [tabActive, setTabActive] = useState<string>(TAB_KEYS.FAVOURITE);
  const [sortBy, setSortBy] = useState<string>("");
  const [sortType, setSortType] = useState<string>("");
  const { pairSetting } = usePairContext();

  const onToggleMenu = () => {
    setIsShow(!isShow);
  };

  const handleClickOutside = (event: Event) => {
    if (contentRef.current && !contentRef.current.contains(event.target)) {
      setIsShow(false);
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside, true);
    return () => {
      document.removeEventListener("click", handleClickOutside, true);
    };
  }, []);

  const isFavorite = true;

  return (
    <div className="relative">
      <div
        onClick={onToggleMenu}
        className="block flex cursor-pointer items-center gap-2"
      >
        <div className="heading-md-medium-18">
          {pairSetting?.baseAsset?.toUpperCase()}/
          {pairSetting?.quoteAsset?.toUpperCase()}
        </div>
        <ChevronDownIcon
          className={`${
            isShow ? "rotate-[180deg]" : ""
          } 'duration-[600]' transition-all`}
        />
      </div>

      {isShow && (
        <div
          ref={contentRef}
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
          }}
          className="border-white-100 absolute left-0 top-[120%] z-[99] min-w-[477px] rounded-[16px] border p-4"
        >
          <div className="border-white-100 flex items-center gap-2 rounded-[6px] border p-2">
            <SearchIcon className="text-white-500" />
            <input
              className="body-md-regular-14 placeholder:text-white-300 bg-transparent outline-none"
              placeholder="Search"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>

          <div className="border-white-100 flex border-b">
            {[...TABS].map((item, index) => {
              return (
                <div
                  onClick={() => setTabActive(item.value)}
                  className={`flex h-[40px] cursor-pointer items-center px-3 py-2.5 ${
                    item.value === tabActive
                      ? "text-white-1000 body-sm-regular-12 border-white-500 border-b"
                      : "text-white-500 body-sm-regular-12"
                  }`}
                  key={index}
                >
                  {item.name}
                </div>
              );
            })}
          </div>

          <div className="border-white-100 grid grid-cols-3 border-b">
            <div className="body-sm-regular-12 text-white-500 flex items-center gap-2 p-2">
              Coin
              <AppButtonSort
                value="coin"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
            <div className="body-sm-regular-12 text-white-500 flex items-center justify-end gap-2">
              Price
              <AppButtonSort
                value="price"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
            <div className="body-sm-regular-12 text-white-500 flex items-center justify-end gap-2">
              24Change
              <AppButtonSort
                value={"24Change"}
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>

          <div className="customer-scroll max-h-[400px] overflow-y-auto">
            <div className="border-white-100 grid grid-cols-3 border-b">
              <div className="body-md-regular-14 text-white-500 flex items-center gap-2 p-2 px-2.5 py-2">
                {isFavorite ? <StarActiveIcon /> : <StarIcon />}
                <div>
                  <span className="text-white-1000">SOL</span>/USDT
                </div>
              </div>
              <div className="flex flex-col items-end px-2.5 py-2">
                <div className="body-md-regular-14 text-white-1000">128</div>
                <div className="text-white-500 body-sm-regular-12">$127.5</div>
              </div>
              <div className="body-md-regular-14 flex items-center justify-end px-2.5 py-2 text-green-500">
                + 0.25%
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

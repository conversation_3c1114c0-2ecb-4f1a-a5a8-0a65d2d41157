"use client";

import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { AppButton, AppDataTableRealtime, AppSelectFilter } from "@/components";
import { CheckboxIcon, CheckboxCheckedIcon, FilterIcon } from "@/assets/icons";
import { useMediaQuery } from "react-responsive";
import { useWindowSize } from "@/hooks";
import DatePicker from "react-datepicker";
import moment from "moment";
import { SelectSideOrder } from "../OrderExchange/components/SelectSideOrder";
import { OrderHistoryItem } from "./components/OrderHistoryItem";
import rf from "@/services/RequestFactory";
import { EOrderStatus, TOpenOrder, TOrderHistory } from "@/types/order";
import { CustomDateInput } from "../TradeHistory";
import { ModalFilterOrderHistory } from "../../modals/ModalFilterOrderHistory";
import { BROADCAST_EVENTS } from "@/libs/broadcast";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";

export enum EOrderSideParam {
  All = "All",
  BUY = "Buy",
  SELL = "Sell",
}

export const OPTIONS_SIDE = [
  {
    label: "All",
    value: EOrderSideParam.All,
  },
  {
    label: "Buy",
    value: EOrderSideParam.BUY,
  },
  {
    label: "Sell",
    value: EOrderSideParam.SELL,
  },
];

const OPTIONS_ORDER_BY = [
  {
    label: "Order Time",
    value: "createdAt",
  },
  {
    label: "Update Time",
    value: "updatedAt",
  },
];

const OPTIONS_BASE = [
  {
    label: "All",
    value: "",
  },
  {
    label: "1000CAT",
    value: "1000CAT",
  },
  {
    label: "1000CHEMS",
    value: "1000CHEMS",
  },
];

const OPTIONS_QUOTE = [
  {
    label: "All",
    value: "",
  },
  {
    label: "USDT",
    value: "USDT",
  },
  {
    label: "USDC",
    value: "USDC",
  },
];

export const TableOrderHistory = memo(
  ({ isInPair = false }: { isInPair: boolean }) => {
    const [sortBy, setSortBy] = useState<string>("createdAt");
    const [side, setSide] = useState<EOrderSideParam>(EOrderSideParam.All);
    // const [sortType, setSortType] = useState<string>("desc");
    const dataTableRef = useRef<HTMLDivElement | null>(null);
    const [isHideAllCancel, setIsHideAllCancel] = useState<boolean>(false);
    const [isShowModalFilter, setIsShowModalFilter] = useState<boolean>(false);

    const [base, setBase] = useState<string>("");
    const [quote, setQuote] = useState<string>("");
    const [dateRange, setDateRange] = useState<[Date, Date]>([
      new Date(),
      new Date(),
    ]);
    const [startDate, endDate] = dateRange;

    const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
    const { windowHeight } = useWindowSize();
    const isLayoutAdvanced = useSelector(
      (state: RootState) => state.metadata.isLayoutAdvanced
    );

    const getData = useCallback(
      async (params: any) => {
        const { docs, cursor } = await rf.getRequest("OrderRequest").getOrders({
          ...params,
          side,
          type: "All",
        });

        try {
          return {
            cursor,
            data: docs || [],
          };
        } catch (err) {
          console.log(err, "getData error");
          return { cursor: null, data: [] };
        }
      },
      [side]
    );

    const tableHeight = useMemo(() => {
      if (isInPair) {
        if (isLayoutAdvanced) {
          return 450;
        }

        return windowHeight - 1010;
      }

      if (isMobile) {
        return windowHeight - 50 - 40 - 36;
      }
      return 200;
    }, [windowHeight, isMobile]);

    //set default range date
    useEffect(() => {
      setDateRange([
        moment().subtract(3, "months").toDate(),
        moment().toDate(),
      ]);
    }, []);

    return (
      <>
        {!isInPair && (
          <div className="my-4 hidden items-center lg:flex">
            <DatePicker
              selectsRange
              startDate={startDate}
              endDate={endDate}
              onChange={(update: any) => setDateRange(update)}
              placeholderText="YYYY/MM/DD - YYYY/MM/DD"
              dateFormat="YYYY/MM/dd"
              customInput={<CustomDateInput isInPair={isInPair} />}
              className="w-full rounded border p-2"
            />
            <div className="ml-2 flex gap-2">
              <div className="min-w-[120px]">
                <AppSelectFilter
                  options={OPTIONS_BASE}
                  value={base}
                  setValue={setBase}
                  title={"Base"}
                />
              </div>
              {/* <div className="min-w-[120px]">
                <AppSelectFilter
                  options={OPTIONS_QUOTE}
                  value={quote}
                  setValue={setQuote}
                  title={"Quote"}
                />
              </div> */}
              <div className="min-w-[120px]">
                <AppSelectFilter
                  options={OPTIONS_SIDE}
                  value={side}
                  setValue={(value: string) =>
                    setSide(value as EOrderSideParam)
                  }
                  title={"Side"}
                />
              </div>
              <div className="min-w-[120px]">
                <AppSelectFilter
                  options={OPTIONS_ORDER_BY}
                  value={sortBy}
                  setValue={setSortBy}
                  title={"Sort By"}
                />
              </div>

              <AppButton className="w-[80px]">Search</AppButton>

              <div className="body-sm-medium-12 flex cursor-pointer items-center px-2">
                Reset
              </div>
            </div>
          </div>
        )}

        <div className="flex items-center justify-between px-4 py-2 lg:hidden">
          <div
            className="body-md-regular-14 flex items-center gap-2"
            onClick={() => setIsHideAllCancel(!isHideAllCancel)}
          >
            {isHideAllCancel ? <CheckboxCheckedIcon /> : <CheckboxIcon />}
            Hide All Cancel
          </div>

          <div onClick={() => setIsShowModalFilter(true)}>
            <FilterIcon />
          </div>
        </div>

        <div className="w-full">
          <AppDataTableRealtime
            minWidth={1108}
            minHeight={300}
            height={tableHeight}
            ref={dataTableRef}
            handleAddNewItem={{
              broadcastName: BROADCAST_EVENTS.ORDER_UPDATED,
              fieldKey: "id",
              formatter: (data: TOpenOrder) => {
                if (
                  [EOrderStatus.NEW, EOrderStatus.PARTIALLY_FILLED].includes(
                    data.status
                  )
                ) {
                  return null;
                }

                return data;
              },
            }}
            getData={getData}
            shouldAutoFetchOnInit
            overrideBodyClassName="w-full"
            renderHeader={() => {
              if (isMobile) {
                return null;
              }
              return (
                <>
                  <div className="flex w-full items-center">
                    <div className="body-sm-regular-12 text-white-500 flex w-[12%] min-w-[100px] items-center px-2 py-1.5 ">
                      Date
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[11%] min-w-[100px] px-2 py-1.5 text-left">
                      Pair
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[11%] min-w-[80px] px-2 py-1.5 text-left">
                      <SelectSideOrder
                        side={side}
                        setSide={(value: string) =>
                          setSide(value as EOrderSideParam)
                        }
                      />
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[11%] min-w-[100px] px-2 py-1.5 text-left">
                      Average
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[11%] min-w-[100px] px-2 py-1.5 text-left">
                      Price
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[11%] min-w-[100px] px-2 py-1.5 text-left">
                      Excuted
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[11%] min-w-[100px] px-2 py-1.5 text-left">
                      Amount
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[11%] min-w-[100px] px-2 py-1.5 text-left">
                      Total
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[11%] min-w-[100px] px-2 py-1.5 text-left">
                      Status
                    </div>
                  </div>
                </>
              );
            }}
            renderRow={(item: TOrderHistory, index: number) => {
              return <OrderHistoryItem key={index} order={item} />;
            }}
          />
        </div>

        {isShowModalFilter && (
          <ModalFilterOrderHistory
            isOpen={isShowModalFilter}
            side={side}
            base={base}
            quote={quote}
            setSide={setSide}
            setQuote={setQuote}
            setBase={setBase}
            optionsBase={OPTIONS_BASE}
            optionsQuote={OPTIONS_QUOTE}
            onClose={() => setIsShowModalFilter(false)}
            endDate={endDate}
            startDate={startDate}
            setDateRange={setDateRange}
          />
        )}
      </>
    );
  }
);

TableOrderHistory.displayName = "TableOrderHistory";

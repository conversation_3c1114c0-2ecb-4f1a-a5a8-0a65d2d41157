import { useEffect, useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { TAccountUpdateWsData, TBalance } from "@/types/account";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import BigNumber from "bignumber.js";
import { TradingPair } from "@/types/pair";

const calculateUsdValue = (
  balance: TBalance,
  tradingPairs: TradingPair[]
): string => {
  const asset = balance.asset?.toUpperCase();

  if (asset === "USDT") {
    return BigNumber(balance.available || 0)
      .plus(balance.locked || 0)
      .decimalPlaces(8, BigNumber.ROUND_DOWN)
      .toFixed();
  }

  const ticker = tradingPairs.find(
    (t) => t.symbol?.toUpperCase() === `${asset?.toUpperCase()}USDT`
  );

  if (!ticker?.lastPrice) {
    return "0";
  }

  const totalBalance = BigNumber(balance.available || 0).plus(
    balance.locked || 0
  );
  return totalBalance
    .multipliedBy(ticker.lastPrice)
    .decimalPlaces(8, BigNumber.ROUND_DOWN)
    .toFixed();
};

const useAccountBalance = ({ coin }: { coin?: string }) => {
  const balances = useSelector(
    (state: RootState) => state.account.account?.balances
  );

  const dispatch = useDispatch();
  const [accountBalances, setAccountBalances] = useState<TBalance[] | []>([]);
  const [coinBalance, setCoinBalance] = useState<TBalance>({
    available: "0",
    locked: "0",
    asset: "",
  });
  const tradingPairs = useSelector(
    (state: RootState) => state.tradingPair.tradingPairs
  );

  const tradingPairsKey = useMemo(() => {
    return tradingPairs
      .map((pair) => `${pair.symbol}:${pair.lastPrice}`)
      .sort()
      .join("|");
  }, [tradingPairs]);

  useEffect(() => {
    const currentBalances = balances || [];

    if (coin) {
      const coinBalance = currentBalances.find(
        (balance) => balance.asset?.toLowerCase() === coin?.toLowerCase()
      );

      if (coinBalance?.asset) {
        const usdValue = calculateUsdValue(coinBalance, tradingPairs);
        setCoinBalance({
          ...coinBalance,
          usdValue: usdValue,
        });
      }
    }

    const balanceWithUsdValue = currentBalances.map((balance) => {
      const usdValue = calculateUsdValue(balance, tradingPairs);
      return {
        ...balance,
        usdValue: usdValue,
      };
    });

    setAccountBalances(balanceWithUsdValue);
  }, [balances, coin, tradingPairs]);

  useEffect(() => {
    const handleAccountUpdate = (event: TBroadcastEvent) => {
      const balanceUpdated: TAccountUpdateWsData = JSON.parse(event.detail);

      const currentBalances = balances || [];
      const assetExists = currentBalances.some(
        (item) =>
          item.asset?.toUpperCase() === balanceUpdated.asset?.toUpperCase()
      );

      let accountBalancesUpdated;

      if (assetExists) {
        accountBalancesUpdated = currentBalances.map((item) => {
          const isSameAsset =
            item.asset?.toUpperCase() === balanceUpdated.asset?.toUpperCase();
          const hasValidOperationId =
            !item.operationId || balanceUpdated.operationId > item.operationId;
          const shouldUpdateBalance = isSameAsset && hasValidOperationId;

          if (shouldUpdateBalance) {
            const updatedBalance = {
              ...item,
              available: balanceUpdated.available,
              locked: balanceUpdated.locked,
              operationId: balanceUpdated.operationId,
            };

            const usdValue = calculateUsdValue(updatedBalance, tradingPairs);
            return {
              ...updatedBalance,
              usdValue,
            };
          }
          return item;
        });
      } else {
        const newBalance = {
          asset: balanceUpdated.asset,
          available: balanceUpdated.available,
          locked: balanceUpdated.locked,
          operationId: balanceUpdated.operationId,
        };

        const usdValue = calculateUsdValue(newBalance, tradingPairs);

        accountBalancesUpdated = [
          ...currentBalances,
          {
            ...newBalance,
            usdValue,
          },
        ];
      }

      setAccountBalances(accountBalancesUpdated);
    };

    AppBroadcast.on(BROADCAST_EVENTS.ACCOUNT_UPDATED, handleAccountUpdate);

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.ACCOUNT_UPDATED,
        handleAccountUpdate
      );
    };
  }, [balances, dispatch]);

  useEffect(() => {
    if (!tradingPairs.length) {
      return;
    }

    const accountBalanceWithUsdValue = accountBalances.map((item) => {
      return {
        ...item,
        usdValue: calculateUsdValue(item, tradingPairs),
      };
    });

    setAccountBalances(accountBalanceWithUsdValue);
  }, [tradingPairs]);

  return {
    accountBalances,
    coinBalance,
  };
};

export default useAccountBalance;

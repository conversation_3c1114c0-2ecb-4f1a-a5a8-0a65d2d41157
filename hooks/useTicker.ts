import { useEffect, useRef, useCallback, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/store";
import { TradingPair } from "@/types/pair";
import { TTickerUpdate } from "@/components/PairMarket/services/TickerHandler";
import {
  getTickerRoom,
  getArrTickerRoom,
  subscribeSocketChannel,
  unsubscribeSocketChannel,
} from "@/libs/socket";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { setTradingPair, setTradingPairs } from "@/store/tradingPair.store";
import { formatTickersUpdate } from "@/utils/format";
import TickerHandler from "@/components/PairMarket/services/TickerHandler";

// Hook for single ticker subscription (for individual pair pages)
export const useSingleTicker = (symbol: string) => {
  const dispatch = useDispatch();
  const { tradingPair } = useSelector((state: RootState) => state.tradingPair);
  const socketConnected = useSelector(
    (state: RootState) => state.metadata.socketConnected
  );

  const tickerHandlerRef = useRef<TickerHandler | null>(null);

  const handleTickerUpdate = useCallback((data: TBroadcastEvent) => {
    if (tickerHandlerRef.current) {
      tickerHandlerRef.current.processTickerUpdate(JSON.parse(data.detail));
    }
  }, []);

  useEffect(() => {
    if (!symbol || !socketConnected) {
      return;
    }

    // Create ticker handler
    const onUpdate = (marketData: TradingPair) => {
      dispatch(setTradingPair(marketData));
    };
    tickerHandlerRef.current = new TickerHandler(symbol, onUpdate);

    // Subscribe to socket channel
    subscribeSocketChannel({
      params: [getTickerRoom(symbol)],
    });

    // Listen to broadcast events
    AppBroadcast.on(BROADCAST_EVENTS.TICKER_UPDATED, handleTickerUpdate);

    return () => {
      // Cleanup
      unsubscribeSocketChannel({
        params: [getTickerRoom(symbol)],
      });
      AppBroadcast.remove(BROADCAST_EVENTS.TICKER_UPDATED, handleTickerUpdate);
      tickerHandlerRef.current = null;
    };
  }, [symbol, socketConnected, dispatch, handleTickerUpdate]);

  return {
    ticker: tradingPair,
    isConnected: socketConnected,
  };
};

export const useMultipleTickers = () => {
  const dispatch = useDispatch();
  const { tradingPairs } = useSelector((state: RootState) => state.tradingPair);
  const socketConnected = useSelector(
    (state: RootState) => state.metadata.socketConnected
  );

  const pairTickersRef = useRef<{ [symbol: string]: TradingPair }>({});
  const frameIdRef = useRef<number | null>(null);
  const [renderCount, setRenderCount] = useState(0);

  const handleArrTickersUpdate = useCallback(
    (data: TBroadcastEvent) => {
      const tickersUpdated = formatTickersUpdate(
        JSON.parse(data.detail) as TTickerUpdate[]
      );

      // Update tickers in ref for performance
      tickersUpdated.forEach((ticker) => {
        const symbol = ticker.symbol;
        if (!pairTickersRef.current[symbol]) {
          pairTickersRef.current[symbol] = {} as TradingPair;
        }
        pairTickersRef.current[symbol] = ticker;
      });

      // Batch updates using requestAnimationFrame
      if (frameIdRef.current !== null) {
        cancelAnimationFrame(frameIdRef.current);
      }

      frameIdRef.current = requestAnimationFrame(() => {
        // Update Redux store with all tickers
        const allTickers = Object.values(pairTickersRef.current);
        dispatch(setTradingPairs(allTickers));
        setRenderCount((prev) => prev + 1);
        frameIdRef.current = null;
      });
    },
    [dispatch]
  );

  useEffect(() => {
    if (!socketConnected) return;

    // Subscribe to array ticker updates
    subscribeSocketChannel({
      params: [getArrTickerRoom()],
    });

    AppBroadcast.on(
      BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
      handleArrTickersUpdate
    );

    return () => {
      // Cleanup
      unsubscribeSocketChannel({
        params: [getArrTickerRoom()],
      });
      AppBroadcast.remove(
        BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
        handleArrTickersUpdate
      );

      // Cancel any pending animation frame
      if (frameIdRef.current !== null) {
        cancelAnimationFrame(frameIdRef.current);
      }
    };
  }, [socketConnected, handleArrTickersUpdate]);

  return {
    tickers: pairTickersRef.current,
    tickersArray: tradingPairs,
    isConnected: socketConnected,
    renderCount, // For components that need to trigger re-renders
  };
};

// Hook for getting specific ticker data from the multiple tickers cache
export const useTickerData = (symbol: string) => {
  const { tickers, isConnected } = useMultipleTickers();

  return {
    ticker: tickers[symbol] || null,
    isConnected,
  };
};

// Advanced hook with throttling and selective updates
export const useOptimizedTickers = (options?: {
  throttleMs?: number;
  symbols?: string[];
  enableBatching?: boolean;
}) => {
  const { throttleMs = 100, symbols, enableBatching = true } = options || {};

  const dispatch = useDispatch();
  const socketConnected = useSelector(
    (state: RootState) => state.metadata.socketConnected
  );

  const pairTickersRef = useRef<{ [symbol: string]: TradingPair }>({});
  const frameIdRef = useRef<number | null>(null);
  const lastUpdateRef = useRef<number>(0);
  const [renderCount, setRenderCount] = useState(0);

  const handleArrTickersUpdate = useCallback(
    (data: TBroadcastEvent) => {
      const now = Date.now();

      // Throttle updates if specified
      if (throttleMs > 0 && now - lastUpdateRef.current < throttleMs) {
        return;
      }

      const tickersUpdated = formatTickersUpdate(
        JSON.parse(data.detail) as TTickerUpdate[]
      );

      // Filter by symbols if specified
      const filteredTickers = symbols
        ? tickersUpdated.filter((ticker) => symbols.includes(ticker.symbol))
        : tickersUpdated;

      if (filteredTickers.length === 0) return;

      // Update tickers in ref
      filteredTickers.forEach((ticker) => {
        const symbol = ticker.symbol;
        if (!pairTickersRef.current[symbol]) {
          pairTickersRef.current[symbol] = {} as TradingPair;
        }
        pairTickersRef.current[symbol] = ticker;
      });

      if (enableBatching) {
        // Batch updates using requestAnimationFrame
        if (frameIdRef.current !== null) {
          cancelAnimationFrame(frameIdRef.current);
        }

        frameIdRef.current = requestAnimationFrame(() => {
          const allTickers = Object.values(pairTickersRef.current);
          dispatch(setTradingPairs(allTickers));
          setRenderCount((prev) => prev + 1);
          frameIdRef.current = null;
          lastUpdateRef.current = Date.now();
        });
      } else {
        // Immediate update
        const allTickers = Object.values(pairTickersRef.current);
        dispatch(setTradingPairs(allTickers));
        setRenderCount((prev) => prev + 1);
        lastUpdateRef.current = now;
      }
    },
    [dispatch, throttleMs, symbols, enableBatching]
  );

  useEffect(() => {
    if (!socketConnected) return;

    subscribeSocketChannel({
      params: [getArrTickerRoom()],
    });

    AppBroadcast.on(
      BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
      handleArrTickersUpdate
    );

    return () => {
      unsubscribeSocketChannel({
        params: [getArrTickerRoom()],
      });
      AppBroadcast.remove(
        BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
        handleArrTickersUpdate
      );

      if (frameIdRef.current !== null) {
        cancelAnimationFrame(frameIdRef.current);
      }
    };
  }, [socketConnected, handleArrTickersUpdate]);

  return {
    tickers: pairTickersRef.current,
    isConnected: socketConnected,
    renderCount,
  };
};

import { useEffect, useRef, useCallback, useState, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/store";
import { TradingPair } from "@/types/pair";
import { TTickerUpdate } from "@/components/PairMarket/services/TickerHandler";
import {
  getTickerRoom,
  getArrTickerRoom,
  subscribeSocketChannel,
  unsubscribeSocketChannel,
} from "@/libs/socket";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { setTradingPair, setTradingPairs } from "@/store/tradingPair.store";
import { formatTickersUpdate } from "@/utils/format";
import TickerHandler from "@/components/PairMarket/services/TickerHandler";

// Singleton ticker manager to prevent duplicate subscriptions
class TickerManager {
  private static instance: TickerManager;
  private subscribers: Set<() => void> = new Set();
  private isSubscribed = false;
  private tickers: { [symbol: string]: TradingPair } = {};
  private frameId: number | null = null;

  static getInstance(): TickerManager {
    if (!TickerManager.instance) {
      TickerManager.instance = new TickerManager();
    }
    return TickerManager.instance;
  }

  private handleArrTickersUpdate = (data: TBroadcastEvent) => {
    const tickersUpdated = formatTickersUpdate(
      JSON.parse(data.detail) as TTickerUpdate[]
    );

    // Update tickers
    tickersUpdated.forEach((ticker) => {
      this.tickers[ticker.symbol] = ticker;
    });

    // Batch notify subscribers using requestAnimationFrame
    if (this.frameId !== null) {
      cancelAnimationFrame(this.frameId);
    }

    this.frameId = requestAnimationFrame(() => {
      this.subscribers.forEach((callback) => callback());
      this.frameId = null;
    });
  };

  subscribe(callback: () => void): () => void {
    this.subscribers.add(callback);

    // Subscribe to socket only on first subscriber
    if (!this.isSubscribed) {
      this.isSubscribed = true;
      subscribeSocketChannel({
        params: [getArrTickerRoom()],
      });
      AppBroadcast.on(
        BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
        this.handleArrTickersUpdate
      );
    }

    // Return unsubscribe function
    return () => {
      this.subscribers.delete(callback);

      // Unsubscribe from socket when no more subscribers
      if (this.subscribers.size === 0 && this.isSubscribed) {
        this.isSubscribed = false;
        unsubscribeSocketChannel({
          params: [getArrTickerRoom()],
        });
        AppBroadcast.remove(
          BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
          this.handleArrTickersUpdate
        );

        if (this.frameId !== null) {
          cancelAnimationFrame(this.frameId);
          this.frameId = null;
        }
      }
    };
  }

  getTickers(): { [symbol: string]: TradingPair } {
    return this.tickers;
  }
}

// Hook for single ticker subscription (for individual pair pages)
export const useSingleTicker = (symbol: string) => {
  const dispatch = useDispatch();
  const { tradingPair } = useSelector((state: RootState) => state.tradingPair);
  const socketConnected = useSelector(
    (state: RootState) => state.metadata.socketConnected
  );

  const tickerHandlerRef = useRef<TickerHandler | null>(null);

  const handleTickerUpdate = useCallback((data: TBroadcastEvent) => {
    if (tickerHandlerRef.current) {
      tickerHandlerRef.current.processTickerUpdate(JSON.parse(data.detail));
    }
  }, []);

  useEffect(() => {
    if (!symbol || !socketConnected) {
      return;
    }

    // Create ticker handler
    const onUpdate = (marketData: TradingPair) => {
      dispatch(setTradingPair(marketData));
    };
    tickerHandlerRef.current = new TickerHandler(symbol, onUpdate);

    // Subscribe to socket channel
    subscribeSocketChannel({
      params: [getTickerRoom(symbol)],
    });

    // Listen to broadcast events
    AppBroadcast.on(BROADCAST_EVENTS.TICKER_UPDATED, handleTickerUpdate);

    return () => {
      // Cleanup
      unsubscribeSocketChannel({
        params: [getTickerRoom(symbol)],
      });
      AppBroadcast.remove(BROADCAST_EVENTS.TICKER_UPDATED, handleTickerUpdate);
      tickerHandlerRef.current = null;
    };
  }, [symbol, socketConnected, dispatch, handleTickerUpdate]);

  return {
    ticker: tradingPair,
    isConnected: socketConnected,
  };
};

export const useMultipleTickers = () => {
  const dispatch = useDispatch();
  const { tradingPairs } = useSelector((state: RootState) => state.tradingPair);
  const socketConnected = useSelector(
    (state: RootState) => state.metadata.socketConnected
  );

  const [renderCount, setRenderCount] = useState(0);
  const tickerManager = TickerManager.getInstance();

  const forceUpdate = useCallback(() => {
    setRenderCount((prev) => prev + 1);

    // Update Redux store with all tickers
    const allTickers = Object.values(tickerManager.getTickers());
    dispatch(setTradingPairs(allTickers));
  }, [dispatch, tickerManager]);

  useEffect(() => {
    if (!socketConnected) return;

    // Subscribe to the singleton ticker manager
    const unsubscribe = tickerManager.subscribe(forceUpdate);

    return unsubscribe;
  }, [socketConnected, forceUpdate, tickerManager]);

  return {
    tickers: tickerManager.getTickers(),
    tickersArray: tradingPairs,
    isConnected: socketConnected,
    renderCount, // For components that need to trigger re-renders
  };
};

// Hook for getting specific ticker data from the multiple tickers cache
export const useTickerData = (symbol: string) => {
  const { tickers, isConnected } = useMultipleTickers();

  return {
    ticker: tickers[symbol] || null,
    isConnected,
  };
};

// Advanced hook with throttling and selective updates (uses singleton manager)
export const useOptimizedTickers = (options?: {
  throttleMs?: number;
  symbols?: string[];
}) => {
  const { throttleMs = 100, symbols } = options || {};
  const { tickers, isConnected, renderCount } = useMultipleTickers();
  const lastUpdateRef = useRef<number>(0);
  const [filteredRenderCount, setFilteredRenderCount] = useState(0);

  // Apply throttling and filtering
  useEffect(() => {
    const now = Date.now();

    // Throttle updates if specified
    if (throttleMs > 0 && now - lastUpdateRef.current < throttleMs) {
      return;
    }

    lastUpdateRef.current = now;
    setFilteredRenderCount((prev) => prev + 1);
  }, [renderCount, throttleMs]);

  // Filter tickers by symbols if specified
  const filteredTickers = useMemo(() => {
    if (!symbols) return tickers;

    const filtered: { [symbol: string]: TradingPair } = {};
    symbols.forEach((symbol) => {
      if (tickers[symbol]) {
        filtered[symbol] = tickers[symbol];
      }
    });
    return filtered;
  }, [tickers, symbols]);

  return {
    tickers: filteredTickers,
    isConnected,
    renderCount: filteredRenderCount,
  };
};

"use client";

import { <PERSON><PERSON>, HeaderMobileHomePage } from "@/layouts";
import { AppButton, AppNumber, AppQRCode } from "@/components";
import React, { useEffect, useMemo, useState } from "react";
import Link from "next/link";
import {
  EyeIcon,
  EyeCloseIcon,
  ChevronDownIcon,
  BookIcon,
} from "@/assets/icons";
import { useSelector } from "react-redux";
import { RootState } from "../store/index";
import { useMediaQuery } from "react-responsive";
import { useRouter } from "next/navigation";
import { TableMarkets } from "./(layout2)/my/dashboard/TableMarket";

import useAccountBalance from "@/hooks/useAccountBalance";
import BigNumber from "bignumber.js";
import PairMarketLanding from "@/components/PairMarketLanding";

const Balance = () => {
  const [isShowBalance, setIsShowBalance] = useState<boolean>(true);
  const { accountBalances } = useAccountBalance({});

  const totalBalanceUsd = useMemo(() => {
    return accountBalances.reduce((acc, item) => {
      return acc.plus(item.usdValue || 0);
    }, new BigNumber(0));
  }, [accountBalances]);

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center justify-between">
        <div className="heading-sm-medium-16 text-white-500 flex items-center gap-2">
          Estimated Balance
          <div
            onClick={() => setIsShowBalance(!isShowBalance)}
            className="cursor-pointer"
          >
            {isShowBalance ? <EyeIcon /> : <EyeCloseIcon />}
          </div>
        </div>
      </div>

      <div>
        <div className=" flex gap-2">
          <div className="heading-xlg-semibold-32">
            <AppNumber
              value={totalBalanceUsd}
              isFormatLargeNumber={false}
              isHide={!isShowBalance}
            />
          </div>

          <div className="heading-sm-medium-16 flex cursor-pointer items-center gap-1">
            USDT <ChevronDownIcon />
          </div>
        </div>

        <div className="heading-sm-medium-16 text-white-800">
          <AppNumber
            value={totalBalanceUsd}
            isFormatLargeNumber={false}
            isForUSD
            isHide={!isShowBalance}
          />
        </div>
      </div>
      {/* <div className="flex items-center gap-2">
        <div className="heading-sm-medium-16 text-white-500 flex items-center gap-2">
          Today&apos;s PnL
          <Tooltip
            placement="top"
            overlay={
              <div className="body-sm-regular-12 max-w-[285px]">
                Today&apos;s PNL = Current asset total - Today&apos;s initial
                asset total - Today&apos;s net transfer and deposit.*The
                calculation of PNL currently does not include data for Alpha
                tokens. The date is only for your reference, there is no
                guarantee that the data is absolutely accurate.
              </div>
            }
          >
            <InforIcon className="h-4 w-4 cursor-pointer" />
          </Tooltip>
        </div>
        {isShowBalance ? (
          <div className="heading-sm-medium-16 text-green-500">
            +$530.87 (1.69%)
          </div>
        ) : (
          <div className="heading-sm-medium-16 text-white-500">******</div>
        )}
      </div> */}
    </div>
  );
};

const ContentMobile = () => {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const router = useRouter();

  useEffect(() => {
    if (accessToken) {
      router.push("/my/dashboard");
    }
  }, [accessToken]);

  return (
    <div>
      <HeaderMobileHomePage />
      <div className="pt-[50px]">
        <div className="flex flex-col gap-4 bg-[url('/images/BgHomePageMobile.png')] bg-cover bg-top bg-no-repeat px-4 pb-4">
          <div className="heading-lg-semibold-24 max-w-[262px]">
            Explore the digital world asset!
          </div>
          <Link href="/login">
            <AppButton size="large" className="w-full">
              Sign Up/ Sign In
            </AppButton>
          </Link>
        </div>

        <div className="px-2">
          <TableMarkets />
        </div>
      </div>
    </div>
  );
};

export default function Home() {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isMobileBreakpoint = useMediaQuery({ query: "(max-width: 992px)" });
  const [isMounted, setIsMounted] = useState<boolean>(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const _renderContentAuth = () => {
    if (!accessToken) {
      return (
        <div className="mt-8 flex gap-4">
          <Link href="/register">
            <AppButton variant="buy" size="large" className="w-[140px]">
              Sign Up
            </AppButton>
          </Link>
          <Link href="/login">
            <AppButton variant="secondary" size="large" className="w-[140px]">
              Sign In
            </AppButton>
          </Link>
        </div>
      );
    }
    return (
      <div className="mt-8">
        <Balance />
        <div className="mt-8 flex items-center gap-4">
          <Link href="#">
            <AppButton variant="buy" size="large" className="w-[140px]">
              Verify Now
            </AppButton>
          </Link>
          <Link href="#">
            <div className="body-md-medium-14 mx-4 flex cursor-pointer items-center gap-1">
              <BookIcon />
              Read Tutorial
            </div>
          </Link>
        </div>
      </div>
    );
  };

  if (!isMounted) return <></>;

  if (isMobileBreakpoint) {
    return <ContentMobile />;
  }

  return (
    <div>
      <Header />
      <div className="pt-[50px]">
        <div className="mx-auto max-w-[1440px]">
          <div className="flex min-h-[646px] max-w-[1440px] items-center bg-[url('/images/BgHomepage.png')] bg-cover bg-top bg-no-repeat px-[100px] py-[56px]">
            <div>
              <div>
                <div className="max-w-[584px] text-[72px] font-extrabold leading-[1.2]">
                  <span className="bg-gradient-text-green ">Get Verified</span>{" "}
                  <span className="bg-gradient-text">& Start Your</span>{" "}
                  <span className="bg-gradient-text"> Crypto Journey</span>
                </div>
              </div>
              {_renderContentAuth()}
            </div>
          </div>

          <div className="flex items-center justify-between px-[100px] pb-[70px]">
            <div className="flex items-center gap-6">
              <div className="border-white-100 flex flex-col items-center rounded-[16px] border p-4">
                <AppQRCode
                  color="rgba(255, 255, 255, 1)"
                  className="h-[166px] w-[166px]"
                  value={"Download App"}
                />

                <div className="body-md-regular-14 text-white-500 mt-2">
                  Scan to Download App
                </div>
                <div className="heading-md-semibold-18">iOS and Android</div>
              </div>
              <div className="heading-lg-medium-24 max-w-[186px]">
                Trade on the go. Anywhere, anytime.
              </div>
            </div>

            <div className="flex flex-col justify-between gap-8">
              <PairMarketLanding />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

"use client";

import React, { memo, useEffect, useState } from "react";
import { AppButtonSort } from "@/components";
import { ChevronDownIcon } from "@/assets/icons";
import { useMediaQuery } from "react-responsive";
import { TBalance } from "@/types/account";
import AppNumber from "@/components/AppNumber";
import _ from "lodash";

const CoinItem = ({ coin }: { coin: TBalance }) => {
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  if (isMobile) {
    return (
      <div className="border-white-50 flex gap-2 border-b py-2">
        <div>{/*image coin*/}</div>
        <div className="flex flex-1 flex-col gap-2">
          <div className="flex justify-between">
            <div className="body-md-medium-14">{coin.asset}</div>
            <div className="body-md-medium-14">
              <AppNumber value={coin.available} />
            </div>
          </div>
          <div className="flex justify-between">
            <div className="body-md-regular-14 text-white-500">Price</div>
            <div className="body-md-regular-14 text-white-500">--</div>
          </div>
          <div className="flex justify-between">
            <div className="body-md-regular-14 text-white-500">
              Today&apos;s PNL
            </div>
            <div className="body-md-regular-14 text-red-400">--</div>
          </div>
          <div className="flex justify-between">
            <div className="body-md-regular-14 text-white-500">Cost Price</div>
            <div className="body-md-regular-14 text-white-500">--</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="border-white-50 hover:bg-white-50 flex w-full cursor-pointer items-center lg:border-b">
      <div className="w-[40%] px-2 py-2.5 text-left lg:min-w-[184px]">
        <div className="flex flex-col justify-end">
          <div className="body-sm-regular-12">{coin.asset}</div>
          {/*<div className="body-xs-regular-10 text-white-500">Solana</div>*/}
        </div>
      </div>
      <div className="body-sm-regular-12 w-[20%] px-2 py-2.5 lg:w-[20%] lg:min-w-[184px]">
        <div className="flex w-full flex-col items-end">
          <div className="body-sm-regular-12">
            <AppNumber value={coin.available} />
          </div>
          <div className="body-xs-regular-10 text-white-500">--</div>
        </div>
      </div>
      <div className="body-sm-regular-12 w-[20%] px-2 py-2.5 lg:w-[20%] lg:min-w-[184px]">
        <div className="flex w-full flex-col items-end">
          <div className="body-sm-regular-12">--</div>
          <div className="body-xs-regular-10 text-white-500">--</div>
        </div>
      </div>
      <div className="body-sm-regular-12 flex w-[20%] items-center justify-end gap-4 px-2 py-2.5 lg:w-[20%] lg:min-w-[184px]">
        <div className="text-right text-right text-green-500">--</div>
        <ChevronDownIcon />
      </div>
    </div>
  );
};

CoinItem.displayName = "CoinItem";

export const TableCoinView = memo(({ balances }: { balances: TBalance[] }) => {
  const [sortBy, setSortBy] = useState<string>("");
  const [sortType, setSortType] = useState<string>("");
  const [isMounted, setIsMounted] = useState<boolean>(false);
  const [balancesShow, setBalancesShow] = useState<TBalance[]>([]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    let dataBalance = balances;

    if (sortType === "desc") {
      dataBalance = _.orderBy(
        dataBalance,
        [
          (coin: any) => {
            if (sortBy === "available") {
              return Number(coin[sortBy]);
            }
            return coin[sortBy];
          },
        ],
        ["desc"]
      );
    }

    if (sortType === "asc") {
      dataBalance = _.orderBy(
        dataBalance,
        [
          (coin: any) => {
            if (sortBy === "available") {
              return Number(coin[sortBy]);
            }
            return coin[sortBy];
          },
        ],
        ["asc"]
      );
    }

    setBalancesShow(dataBalance);
  }, [balances, sortBy, sortType]);

  if (!isMounted) return <></>;

  return (
    <>
      <div className="w-full">
        <div className="hidden w-full items-center md:flex">
          <div className="body-sm-regular-12 text-white-500 flex w-[40%] items-center px-2 py-1.5 md:min-w-[184px] ">
            <div className="flex items-center gap-2">
              Coin
              <AppButtonSort
                value="asset"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-left md:min-w-[184px]">
            <div className="flex items-center justify-end gap-2">
              Amount
              <AppButtonSort
                value="available"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-left md:min-w-[184px]">
            <div className="flex items-center justify-end gap-2">
              Price
              <AppButtonSort
                value="price"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-left md:min-w-[184px]">
            <div className="flex items-center justify-end gap-2">
              Today&apos;s PnL
              <AppButtonSort
                value="pnl"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
        </div>

        <div className="w-full">
          {!!balancesShow?.length ? (
            balancesShow.map((coin: TBalance, index: number) => {
              return <CoinItem key={index} coin={coin} />;
            })
          ) : (
            <div className="body-sm-regular-12 mt-8 text-center">
              No Data...
            </div>
          )}
        </div>
      </div>
    </>
  );
});

TableCoinView.displayName = "TableCoinView";

# Ticker Logic Optimization Guide

## Overview

This guide explains the benefits of moving ticker logic to centralized `useTicker` hooks and provides migration examples for smooth performance improvements.

## Current Issues

### Performance Problems
1. **Multiple Subscriptions**: Components independently subscribe to the same ticker data
2. **Duplicated Logic**: Similar ticker handling code repeated across components
3. **Memory Leaks**: Multiple event listeners and subscriptions
4. **Inconsistent Updates**: Different components use different optimization patterns
5. **Re-render Issues**: Unnecessary re-renders on every ticker update

### Current Implementation Analysis
- `PairTicker.tsx`: Manual subscription + Redux dispatch
- `MarketExplorer.tsx`: useRef + requestAnimationFrame optimization
- `PairMarketLanding.tsx`: Similar pattern to MarketExplorer
- `app/trade/[symbol]/provider.tsx`: TickerHandler class + Redux dispatch
- `app/(layout2)/layout.tsx`: Global ticker subscription

## New `useTicker` Hooks

### 1. `useSingleTicker(symbol: string)`
**Use Case**: Individual trading pair pages
**Benefits**:
- Automatic subscription management
- Built-in cleanup
- Redux integration
- Performance optimized

```typescript
const { ticker, isConnected } = useSingleTicker(symbol);
```

### 2. `useMultipleTickers()`
**Use Case**: Market explorer, landing pages
**Benefits**:
- Batched updates with requestAnimationFrame
- useRef for performance
- Centralized subscription
- Automatic cleanup

```typescript
const { tickers, tickersArray, isConnected, renderCount } = useMultipleTickers();
```

### 3. `useTickerData(symbol: string)`
**Use Case**: Components that need specific ticker from global cache
**Benefits**:
- No additional subscriptions
- Leverages existing multiple tickers subscription
- Lightweight

```typescript
const { ticker, isConnected } = useTickerData(symbol);
```

### 4. `useOptimizedTickers(options)`
**Use Case**: Advanced scenarios with custom throttling
**Benefits**:
- Configurable throttling
- Symbol filtering
- Batching control

```typescript
const { tickers, isConnected, renderCount } = useOptimizedTickers({
  throttleMs: 100,
  symbols: ['BTCUSDT', 'ETHUSDT'],
  enableBatching: true
});
```

## Migration Examples

### Before: PairTicker.tsx
```typescript
// Manual subscription and cleanup
useEffect(() => {
  if (!symbol || !socketConnected) return;
  
  subscribeSocketChannel({
    params: [getTickerRoom(symbol)],
  });

  return () => {
    unsubscribeSocketChannel({
      params: [getTickerRoom(symbol)],
    });
  };
}, [symbol, socketConnected]);

// Separate Redux selector
const { tradingPair } = useSelector((state: RootState) => state.tradingPair);
```

### After: PairTicker.optimized.tsx
```typescript
// Single hook handles everything
const { ticker: tradingPair, isConnected } = useSingleTicker(symbol);
```

### Before: MarketExplorer.tsx
```typescript
// Complex manual optimization
const pairTickersRef = useRef<{ [symbol: string]: TradingPair }>({});
const frameIdRef = useRef<number | null>(null);

const handleArrTickersUpdate = useCallback((data: TBroadcastEvent) => {
  // 30+ lines of manual optimization logic
}, []);

useEffect(() => {
  // Manual subscription setup
}, [handleArrTickersUpdate, socketConnected]);
```

### After: MarketExplorer.optimized.tsx
```typescript
// Single hook with built-in optimization
const { tickers, isConnected, renderCount } = useMultipleTickers();
```

## Performance Benefits

### 1. Reduced Subscriptions
- **Before**: 4+ separate subscriptions to same ticker data
- **After**: 1 centralized subscription per data type

### 2. Memory Efficiency
- **Before**: Multiple event listeners and refs
- **After**: Shared resources and automatic cleanup

### 3. Consistent Optimization
- **Before**: Inconsistent patterns across components
- **After**: Proven optimization patterns in all hooks

### 4. Better Developer Experience
- **Before**: 30+ lines of boilerplate per component
- **After**: 1 line hook call

### 5. Reduced Bundle Size
- **Before**: Duplicated logic in multiple components
- **After**: Centralized logic in hooks

## Migration Strategy

### Phase 1: Create Hooks (✅ Complete)
- [x] Create `hooks/useTicker.ts`
- [x] Implement all hook variants
- [x] Add TypeScript types

### Phase 2: Create Optimized Components
- [x] Create `PairTicker.optimized.tsx`
- [x] Create `MarketExplorer.optimized.tsx`
- [ ] Create `PairMarketLanding.optimized.tsx`

### Phase 3: Update Providers
- [ ] Update `app/trade/[symbol]/provider.tsx`
- [ ] Update `app/(layout2)/layout.tsx`

### Phase 4: Replace Original Components
- [ ] Replace `PairTicker.tsx` with optimized version
- [ ] Replace `MarketExplorer.tsx` with optimized version
- [ ] Replace `PairMarketLanding.tsx` with optimized version

### Phase 5: Cleanup
- [ ] Remove unused ticker logic
- [ ] Remove duplicate subscriptions
- [ ] Update tests

## Testing Strategy

### Performance Testing
1. **Memory Usage**: Monitor memory consumption before/after
2. **Re-render Count**: Use React DevTools Profiler
3. **Network Requests**: Verify reduced WebSocket subscriptions
4. **CPU Usage**: Monitor during high-frequency updates

### Functional Testing
1. **Ticker Updates**: Verify all components receive updates
2. **Subscription Management**: Test connection/disconnection
3. **Error Handling**: Test WebSocket failures
4. **Memory Leaks**: Test component unmounting

## Recommended Implementation

**YES, you should move all ticker logic to the `useTicker` hooks** for the following reasons:

1. **Significant Performance Gains**: Reduced subscriptions, memory usage, and CPU overhead
2. **Better Code Maintainability**: Centralized logic, consistent patterns
3. **Improved Developer Experience**: Simple API, automatic optimization
4. **Future-Proof**: Easy to add features like throttling, filtering
5. **Memory Leak Prevention**: Automatic cleanup and resource management

The hooks follow your preferred patterns from the memories:
- Use `useRef` for performance optimization
- Implement `requestAnimationFrame` batching
- Follow patterns from orderbook component
- Centralize logic to avoid synchronization issues
